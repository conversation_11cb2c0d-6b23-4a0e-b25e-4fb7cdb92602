import torch
import torch.nn as nn
from typing import Optional


class TaskGraph(nn.Module):
    """
    动态任务图谱模块
    
    管理任务图的静态初始权重和动态更新过程
    支持训练时的非累积更新和推理时的累积更新
    """
    
    def __init__(self, W0: torch.Tensor, proto: torch.Tensor):
        """
        Args:
            W0: 初始任务图权重矩阵，形状为 (M, M)，以logits形式存储
            proto: 动作原型矩阵，形状为 (M, D)，每行是一个动作类别的原型特征
        """
        super().__init__()
        
        # 将W0和proto注册为buffer，不参与梯度更新但会随模型移动到GPU
        self.register_buffer("W0", W0)
        self.register_buffer("proto", proto)
        
        # 推理时使用的动态图权重
        self.W: Optional[torch.Tensor] = None
        self.reset()

    def reset(self):
        """
        重置推理时的图状态
        
        必须使用clone()，以确保每次重置都从原始W0开始，
        并正确处理设备转移（如.to('cuda')）
        """
        self.W = self.W0.clone()

    def get_next_step_logits(self, current_node_idx: int, delta_row: torch.Tensor) -> torch.Tensor:
        """
        用于训练：基于W0进行非累积更新，计算下一步的logits

        Args:
            current_node_idx: 当前节点索引
            delta_row: 权重调整量，形状为 (M,)

        Returns:
            下一步的logits，形状为 (M,)

        Raises:
            ValueError: 如果节点索引超出范围或delta_row维度不匹配
        """
        if not (0 <= current_node_idx < self.W0.shape[0]):
            raise ValueError(f"Node index {current_node_idx} out of range [0, {self.W0.shape[0]})")

        if delta_row.shape != (self.W0.shape[1],):
            raise ValueError(f"Expected delta_row shape {(self.W0.shape[1],)}, got {delta_row.shape}")

        return self.W0[current_node_idx] + delta_row
        
    def step_and_update(self, node_idx: int, delta_row: torch.Tensor):
        """
        用于推理：在当前图上执行累积的、原地的更新

        Args:
            node_idx: 要更新的节点索引
            delta_row: 权重调整量，形状为 (M,)

        Raises:
            RuntimeError: 如果图未初始化
            ValueError: 如果节点索引超出范围或delta_row维度不匹配
        """
        if self.W is None:
            raise RuntimeError("Graph not initialized. Call reset() first.")

        if not (0 <= node_idx < self.W.shape[0]):
            raise ValueError(f"Node index {node_idx} out of range [0, {self.W.shape[0]})")

        if delta_row.shape != (self.W.shape[1],):
            raise ValueError(f"Expected delta_row shape {(self.W.shape[1],)}, got {delta_row.shape}")

        # 在多GPU场景或混合精度下，确保设备一致性
        if self.W.device != delta_row.device:
            delta_row = delta_row.to(self.W.device)

        self.W[node_idx] += delta_row
    
    def get_current_weights(self) -> torch.Tensor:
        """
        获取当前的图权重矩阵
        
        Returns:
            当前权重矩阵，形状为 (M, M)
        """
        return self.W if self.W is not None else self.W0
    
    def get_transition_probs(self, node_idx: int) -> torch.Tensor:
        """
        获取从指定节点出发的转移概率
        
        Args:
            node_idx: 节点索引
            
        Returns:
            转移概率，形状为 (M,)
        """
        logits = self.get_current_weights()[node_idx]
        return torch.softmax(logits, dim=0)
