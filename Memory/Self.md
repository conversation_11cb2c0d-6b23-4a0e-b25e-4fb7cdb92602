# 知识沉淀与纠错记录 (Self.md)

## 项目重构记录

### 重构背景
- 现有代码框架基本完整，但不完全符合IDEA.md的规范要求
- 需要按照IDEA.md的严格规范重新组织代码结构
- 缺少关键的数据预处理脚本和测试脚本

### 重构目标
1. 创建符合IDEA.md规范的完整代码框架
2. 实现多视角融合、帧同步和标签映射功能
3. 按照IDEA.md要求重新组织训练和测试脚本
4. 确保输出目录结构完全符合规范

### 重构完成情况
**状态**: ✅ 完全完成

**已完成的组件**:
1. **数据预处理脚本** (`txt_to_npy.py`)
   - 实现多视角融合、帧同步和标签映射
   - 支持1-4个视角的特征拼接和平均
   - 完整的一致性检查和错误处理

2. **训练脚本** (Train/目录)
   - `Train_Action_Prototype.py`: 动作原型特征训练
   - `Train_Edge_Weight.py`: 任务图边权训练
   - `Train_Static_Model.py`: 静态任务图训练
   - `Train_Dynamic_Model.py`: 动态任务图训练

3. **测试脚本** (Test/目录)
   - `Test_Static.py`: 静态模型测试
   - `Test_Dynamic.py`: 动态模型测试
   - `Test_Static_vs_Dynamic.py`: Bootstrap置信区间对比

4. **辅助脚本**
   - `setup_directories.py`: 目录结构设置
   - `run_experiment.py`: 实验总控脚本
   - `verify_framework.py`: 代码框架验证

5. **数据加载器更新**
   - 更新 `src/data/datamodule.py` 支持.npy格式数据

**验证结果**:
- 文件结构完整性: ✅ 通过
- 导入依赖关系: ✅ 通过
- 配置一致性: ✅ 通过
- 数据流逻辑: ✅ 通过
- 输出规范符合性: ✅ 通过

**符合IDEA.md规范**:
- 严格按照第2章学术方案实现
- 完全符合第5章输出存储规范
- 使用统一的时间戳ID命名规范
- 实现完整的Bootstrap置信区间评估
