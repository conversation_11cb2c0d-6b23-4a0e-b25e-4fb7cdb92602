# Linux_Plan_A: 动态任务图谱差分更新实验

[![PyTorch](https://img.shields.io/badge/PyTorch-2.5.1-red.svg)](https://pytorch.org/)
[![Lightning](https://img.shields.io/badge/Lightning-2.5.1-purple.svg)](https://lightning.ai/)
[![CUDA](https://img.shields.io/badge/CUDA-11.8-green.svg)](https://developer.nvidia.com/cuda-toolkit)
[![GPU](https://img.shields.io/badge/GPU-A6000_48GB-blue.svg)](https://www.nvidia.com/en-us/data-center/a6000/)

> 基于Plan_Min.md规范的动态任务图谱差分更新方法实现，专门针对Breakfast数据集中的cereals任务（48个动作类别）。

## 🎯 项目目标

实现并对比两种任务图谱方法：

- **静态基线**: 使用固定任务图谱W₀进行动作预测
- **动态方法**: 根据特征差异动态更新任务图谱权重

## 🚀 快速使用

### 一键运行完整实验

```bash
# 激活环境
conda activate sci_1

# 运行完整对比实验（推荐）
python run_experiments.py
```

### 单独训练方法

```bash
# 训练静态基线
python train_static.py

# 训练动态方法
python train_dynamic.py

# 评估训练好的模型
python evaluate_model.py
```

## 📊 训练结果

实验在NVIDIA RTX A6000 (48GB)上完成，主要输出：

```
/data2/syd_data/Breakfast_Data/Outputs/
├── static_baseline/            # 静态基线结果
│   ├── checkpoints/           # 模型权重
│   ├── final_static_model.ckpt
│   └── static_baseline_logs/  # TensorBoard日志
├── dynamic_method/            # 动态方法结果  
│   ├── checkpoints/           # 模型权重
│   ├── final_dynamic_model.ckpt
│   └── dynamic_method_logs/   # TensorBoard日志
└── experiment_report.json     # 实验配置报告
```

## 🔧 配置说明

### GPU优化配置

- **Batch Size**: 64 (充分利用A6000内存)
- **Mixed Precision**: 16-bit (提升训练速度)
- **Workers**: 8 (数据加载并行化)
- **CUDA Optimization**: cuDNN benchmark启用

### 模型参数

- **特征维度 (D)**: 64
- **动作类别 (M)**: 48 (完整的cereals任务动作集合，符合Plan_Min.md规范)
- **隐藏层维度 (H)**: 256
- **差分权重因子 (α)**: 0.05 (动态方法) / 0.05 (静态基线)

## 📈 性能监控

```bash
# GPU监控
watch -n 1 nvidia-smi

# TensorBoard可视化
tensorboard --logdir /data2/syd_data/Breakfast_Data/Outputs

# 训练进程检查
ps aux | grep python | grep train
```

## 📁 核心文件

| 文件 | 功能 | 输出 |
|------|------|------|
| `run_experiments.py` | 完整实验运行 | 两种方法的对比结果 |
| `train_static.py` | 静态基线训练 | 基线模型和性能指标 |
| `train_dynamic.py` | 动态方法训练 | 动态模型和改进效果 |
| `evaluate_model.py` | 模型评估和报告生成 | 详细评估报告和可视化 |
| `configs/config.yaml` | 主配置文件 | 动态方法训练配置 |
| `configs/config_static.yaml` | 静态方法配置 | 静态基线训练配置 |
| `configs/config_dynamic.yaml` | 动态方法配置 | 差分更新参数设置 |

## 🧠 技术实现

### 静态基线方法

- 使用固定的任务图邻接矩阵W₀
- 基于当前动作节点预测下一动作
- 提供性能下界参考

### 动态差分更新方法

- 计算特征与原型的差异: `diff = |V_k - proto[n_k]|`
- 通过MLP预测权重调整: `ΔW_k = α * tanh(MLP(diff))`
- 训练时非累积更新: `logits_k = W₀[n_k] + ΔW_k`
- 推理时累积更新: `W_k[n_k] = W_{k-1}[n_k] + ΔW_k`

## 🔬 技术规范

### 数学公式实现

严格遵循Plan_Min.md中的数学公式：

1. **原型计算**: $\mathcal V_{n}=\frac{1}{|\mathcal T_{n}|}\sum_{(k\mid n_{k}=n)} V_{k}$
2. **静态图初始化**: $W^{0}_{ij}=\log\left(\frac{\text{Count}(n_{t}=i\wedge n_{t+1}=j) + \epsilon}{\sum_{j'} \text{Count}(n_{t}=i\wedge n_{t+1}=j') + M\epsilon}\right)$
3. **差分特征**: $\text{Diff}_{k}=|V_{k}-\mathcal V_{n_{k}}|$
4. **权重调整**: $\Delta W_{k}=\alpha\;\mathrm{tanh}(W_{2}\,\sigma(W_{1}\,\text{Diff}_{k}+b_{1})+b_{2})$
5. **训练logits**: $\text{logits}_{k} = W^{0}_{n_k,*} + \Delta W_{k}$
6. **推理图更新**: $W^{k}_{i,*} = W^{k-1}_{i,*} + \Delta W_{k}$ (当 $i=n_{k}$)

### 评估指标

- **时序段级IoU**: 真正的时序重叠度，衡量预测段与真实段的时间对齐程度
- **帧级准确度**: 逐帧预测正确率，衡量细粒度预测精度
- **动作覆盖率**: 动作类型覆盖度，衡量预测动作集合与真实动作集合的重叠
- **归一化编辑距离**: 序列相似度，值越小表示序列越相似

## 🎓 学术应用

本项目支持学术研究需求：

- 训练日志自动记录，支持TensorBoard可视化
- 模型权重保存，便于后续分析
- 实验配置完整记录，确保可重现性
- GPU性能优化，适合大规模实验

## 📋 系统要求

- **操作系统**: Linux (Ubuntu 18.04+)
- **GPU**: NVIDIA RTX A6000 或同等性能GPU
- **CUDA**: 11.8
- **PyTorch**: 2.4.1
- **Python**: 3.8+
- **内存**: 推荐16GB+ 系统内存

---

详细使用说明请参考 [USAGE_GUIDE.md](USAGE_GUIDE.md)
