#!/usr/bin/env python3
"""
动作原型特征训练脚本 - Train_Action_Prototype.py
根据IDEA.md第2.1节规范实现

主要功能：
1. 读取训练集中所有.npy文件
2. 根据动作标签计算每个类别的原型特征
3. 保存原型特征文件action_prototypes.pt
4. 生成训练过程的详细数据和可视化图表
"""

import os
import sys
import torch
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置可视化风格
sns.set_theme(style="whitegrid")

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
LABEL_MAP_PATH = os.path.join(DATA_ROOT, "label_map.json")
TRAIN_DATA_DIR = os.path.join(DATA_ROOT, "breakfast_data_npy")
TRAIN_LABEL_DIR = os.path.join(DATA_ROOT, "segmentation_coarse_npy")

# 输出路径配置（符合IDEA.md第5章规范）
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs", "Action_Prototype")
MODEL_PARAMS_DIR = os.path.join(OUTPUT_BASE, "Model_parameters")
RAW_DATA_DIR = os.path.join(OUTPUT_BASE, "Raw_data")
VISUALIZATION_DIR = os.path.join(OUTPUT_BASE, "Visualization")

# 生成时间戳ID
TIMESTAMP_ID = datetime.now().strftime("%Y%m%d-%H%M%S")


class ActionPrototypeTrainer:
    """动作原型特征训练器"""
    
    def __init__(self):
        self.label_map = self.load_label_map()
        self.M = len(self.label_map)  # 动作类别总数
        self.D = 64  # 特征维度
        
        # 创建输出目录
        os.makedirs(MODEL_PARAMS_DIR, exist_ok=True)
        os.makedirs(RAW_DATA_DIR, exist_ok=True)
        os.makedirs(VISUALIZATION_DIR, exist_ok=True)
        
        print(f"🧠 动作原型训练器初始化")
        print(f"   - 动作类别数: {self.M}")
        print(f"   - 特征维度: {self.D}")
        print(f"   - 时间戳ID: {TIMESTAMP_ID}")
    
    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        with open(LABEL_MAP_PATH, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        print(f"📋 加载标签映射: {len(label_map)} 个动作类别")
        return label_map
    
    def find_training_files(self) -> List[Tuple[str, str]]:
        """查找训练文件对（特征文件，标签文件）"""
        file_pairs = []
        
        # 扫描s1, s2, s3的训练数据
        for subject in ["s1", "s2", "s3"]:
            subject_feat_dir = os.path.join(TRAIN_DATA_DIR, subject)
            subject_label_dir = os.path.join(TRAIN_LABEL_DIR, f"{subject}_label")
            
            if not os.path.exists(subject_feat_dir) or not os.path.exists(subject_label_dir):
                print(f"⚠️ 跳过不存在的目录: {subject}")
                continue
            
            # 遍历任务目录
            for task_dir in os.listdir(subject_feat_dir):
                feat_task_path = os.path.join(subject_feat_dir, task_dir)
                label_task_path = os.path.join(subject_label_dir, task_dir)
                
                if not os.path.isdir(feat_task_path) or not os.path.isdir(label_task_path):
                    continue
                
                # 匹配特征文件和标签文件
                for feat_file in os.listdir(feat_task_path):
                    if feat_file.endswith('.npy'):
                        feat_path = os.path.join(feat_task_path, feat_file)
                        label_file = feat_file  # 假设文件名相同
                        label_path = os.path.join(label_task_path, label_file)
                        
                        if os.path.exists(label_path):
                            file_pairs.append((feat_path, label_path))
        
        print(f"📁 找到 {len(file_pairs)} 个训练文件对")
        return file_pairs
    
    def compute_prototypes(self, file_pairs: List[Tuple[str, str]]) -> Tuple[torch.Tensor, Dict]:
        """
        计算动作原型特征
        
        Returns:
            prototypes: 原型特征矩阵 (M, D)
            stats: 统计信息字典
        """
        print("🔄 开始计算动作原型特征...")
        
        # 初始化累积器
        proto_sum = torch.zeros((self.M, self.D), dtype=torch.float32)
        proto_count = torch.zeros(self.M, dtype=torch.float32)
        
        # 统计信息
        stats = {
            'sample_counts': {},  # 每个类别的样本数
            'class_variances': {},  # 每个类别的内部方差
            'total_samples': 0,
            'processed_files': 0,
            'skipped_files': 0
        }
        
        # 遍历所有文件对
        for i, (feat_path, label_path) in enumerate(file_pairs):
            try:
                # 加载特征和标签
                features = torch.from_numpy(np.load(feat_path)).float()  # (N, D)
                labels = torch.from_numpy(np.load(label_path)).long()    # (N,)
                
                # 确保维度匹配
                min_len = min(len(features), len(labels))
                features = features[:min_len]
                labels = labels[:min_len]
                
                # 累积每个动作类别的特征
                for n_k in range(self.M):
                    mask = (labels == n_k)
                    if mask.any():
                        class_features = features[mask]
                        proto_sum[n_k] += class_features.sum(dim=0)
                        proto_count[n_k] += mask.sum()
                
                stats['processed_files'] += 1
                stats['total_samples'] += len(features)
                
                if (i + 1) % 50 == 0:
                    print(f"   处理进度: {i + 1}/{len(file_pairs)}")
                
            except Exception as e:
                print(f"⚠️ 处理文件失败 {feat_path}: {e}")
                stats['skipped_files'] += 1
                continue
        
        # 计算原型特征
        prototypes = torch.zeros((self.M, self.D), dtype=torch.float32)
        for n_k in range(self.M):
            if proto_count[n_k] > 0:
                prototypes[n_k] = proto_sum[n_k] / proto_count[n_k]
                stats['sample_counts'][str(n_k)] = int(proto_count[n_k].item())
            else:
                stats['sample_counts'][str(n_k)] = 0
        
        # 计算类别内部方差
        print("🔄 计算类别内部方差...")
        class_variances = torch.zeros(self.M, dtype=torch.float32)
        
        for i, (feat_path, label_path) in enumerate(file_pairs):
            try:
                features = torch.from_numpy(np.load(feat_path)).float()
                labels = torch.from_numpy(np.load(label_path)).long()
                
                min_len = min(len(features), len(labels))
                features = features[:min_len]
                labels = labels[:min_len]
                
                for n_k in range(self.M):
                    mask = (labels == n_k)
                    if mask.any() and proto_count[n_k] > 1:
                        class_features = features[mask]
                        # 计算与原型的平均距离
                        distances = torch.norm(class_features - prototypes[n_k], dim=1)
                        class_variances[n_k] += distances.sum()
                
            except Exception:
                continue
        
        # 归一化方差
        for n_k in range(self.M):
            if proto_count[n_k] > 1:
                class_variances[n_k] /= proto_count[n_k]
                stats['class_variances'][str(n_k)] = float(class_variances[n_k].item())
            else:
                stats['class_variances'][str(n_k)] = 0.0
        
        print(f"✅ 原型计算完成:")
        print(f"   - 有数据的动作类别: {(proto_count > 0).sum().item()}/{self.M}")
        print(f"   - 总样本数: {stats['total_samples']}")
        print(f"   - 处理文件数: {stats['processed_files']}")
        
        return prototypes, stats

    def save_results(self, prototypes: torch.Tensor, stats: Dict):
        """保存训练结果"""
        print("💾 保存训练结果...")

        # 1. 保存模型参数
        model_path = os.path.join(MODEL_PARAMS_DIR, f"action_prototypes_{TIMESTAMP_ID}.pt")
        torch.save(prototypes, model_path)
        print(f"   ✅ 原型特征已保存: {model_path}")

        # 2. 保存原始数据
        raw_data = {
            'prototypes': prototypes,
            'statistics': stats,
            'metadata': {
                'timestamp_id': TIMESTAMP_ID,
                'num_classes': self.M,
                'feature_dim': self.D,
                'label_map': self.label_map
            }
        }

        raw_data_path = os.path.join(RAW_DATA_DIR, f"prototype_raw_data_{TIMESTAMP_ID}.pt")
        torch.save(raw_data, raw_data_path)

        # 保存统计信息为JSON
        stats_path = os.path.join(RAW_DATA_DIR, f"prototype_statistics_{TIMESTAMP_ID}.json")
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

        print(f"   ✅ 原始数据已保存: {raw_data_path}")
        print(f"   ✅ 统计信息已保存: {stats_path}")

    def generate_visualizations(self, prototypes: torch.Tensor, stats: Dict):
        """生成可视化图表"""
        print("📊 生成可视化图表...")

        # 1. 样本数分布图
        plt.figure(figsize=(12, 6))
        sample_counts = [stats['sample_counts'][str(i)] for i in range(self.M)]
        action_names = [self.label_map.get(str(i), f"Action_{i}") for i in range(self.M)]

        plt.bar(range(self.M), sample_counts)
        plt.xlabel('动作类别')
        plt.ylabel('样本数量')
        plt.title('各动作类别样本数分布')
        plt.xticks(range(self.M), action_names, rotation=45, ha='right')
        plt.tight_layout()

        sample_dist_path = os.path.join(VISUALIZATION_DIR, f"sample_distribution_{TIMESTAMP_ID}.png")
        plt.savefig(sample_dist_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 类别内部方差图
        plt.figure(figsize=(12, 6))
        variances = [stats['class_variances'][str(i)] for i in range(self.M)]

        plt.bar(range(self.M), variances)
        plt.xlabel('动作类别')
        plt.ylabel('内部方差')
        plt.title('各动作类别内部方差')
        plt.xticks(range(self.M), action_names, rotation=45, ha='right')
        plt.tight_layout()

        variance_path = os.path.join(VISUALIZATION_DIR, f"class_variances_{TIMESTAMP_ID}.png")
        plt.savefig(variance_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 3. 原型特征热力图
        plt.figure(figsize=(16, 10))

        # 只显示有数据的类别
        valid_classes = [i for i in range(self.M) if stats['sample_counts'][str(i)] > 0]
        if valid_classes:
            valid_prototypes = prototypes[valid_classes].numpy()
            valid_names = [action_names[i] for i in valid_classes]

            sns.heatmap(valid_prototypes,
                       yticklabels=valid_names,
                       cmap='viridis',
                       center=0)
            plt.title('动作原型特征热力图')
            plt.xlabel('特征维度')
            plt.ylabel('动作类别')
            plt.tight_layout()

            heatmap_path = os.path.join(VISUALIZATION_DIR, f"prototype_heatmap_{TIMESTAMP_ID}.png")
            plt.savefig(heatmap_path, dpi=300, bbox_inches='tight')
            plt.close()

        print(f"   ✅ 可视化图表已保存到: {VISUALIZATION_DIR}")

    def run_training(self):
        """执行完整的训练流程"""
        print("=" * 80)
        print("🚀 动作原型特征训练开始")
        print("=" * 80)

        # 1. 查找训练文件
        file_pairs = self.find_training_files()
        if not file_pairs:
            print("❌ 未找到训练文件!")
            return

        # 2. 计算原型特征
        prototypes, stats = self.compute_prototypes(file_pairs)

        # 3. 保存结果
        self.save_results(prototypes, stats)

        # 4. 生成可视化
        self.generate_visualizations(prototypes, stats)

        print("=" * 80)
        print("✅ 动作原型特征训练完成!")
        print(f"📁 输出目录: {OUTPUT_BASE}")
        print(f"🏷️ 时间戳ID: {TIMESTAMP_ID}")
        print("=" * 80)


def main():
    """主函数"""
    trainer = ActionPrototypeTrainer()
    trainer.run_training()


if __name__ == "__main__":
    main()
