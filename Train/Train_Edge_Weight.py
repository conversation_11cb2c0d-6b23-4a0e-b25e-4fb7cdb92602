#!/usr/bin/env python3
"""
任务图边权训练脚本 - Train_Edge_Weight.py
根据IDEA.md第2.2节规范实现

主要功能：
1. 统计训练集中所有相邻动作的转移频次
2. 计算平滑后的条件概率P(n_j|n_i)
3. 取对数得到初始的边权重W_ij^0
4. 保存edge_weights.pt和edge_weights.csv
5. 生成训练过程的详细数据和可视化图表
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置可视化风格
sns.set_theme(style="whitegrid")

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
LABEL_MAP_PATH = os.path.join(DATA_ROOT, "label_map.json")
TRAIN_LABEL_DIR = os.path.join(DATA_ROOT, "segmentation_coarse_npy")

# 输出路径配置（符合IDEA.md第5章规范）
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs", "Edge_Weight")
MODEL_PARAMS_DIR = os.path.join(OUTPUT_BASE, "Model_parameters")
RAW_DATA_DIR = os.path.join(OUTPUT_BASE, "Raw_data")
VISUALIZATION_DIR = os.path.join(OUTPUT_BASE, "Visualization")

# 生成时间戳ID
TIMESTAMP_ID = datetime.now().strftime("%Y%m%d-%H%M%S")


class EdgeWeightTrainer:
    """任务图边权训练器"""
    
    def __init__(self, alpha: float = 1.0):
        """
        Args:
            alpha: 平滑因子（Laplace平滑中，alpha=1）
        """
        self.alpha = alpha
        self.label_map = self.load_label_map()
        self.M = len(self.label_map)  # 动作类别总数
        
        # 创建输出目录
        os.makedirs(MODEL_PARAMS_DIR, exist_ok=True)
        os.makedirs(RAW_DATA_DIR, exist_ok=True)
        os.makedirs(VISUALIZATION_DIR, exist_ok=True)
        
        print(f"🔗 任务图边权训练器初始化")
        print(f"   - 动作类别数: {self.M}")
        print(f"   - 平滑因子α: {self.alpha}")
        print(f"   - 时间戳ID: {TIMESTAMP_ID}")
    
    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        with open(LABEL_MAP_PATH, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        print(f"📋 加载标签映射: {len(label_map)} 个动作类别")
        return label_map
    
    def find_training_label_files(self) -> List[str]:
        """查找训练标签文件"""
        label_files = []
        
        # 扫描s1, s2, s3的训练标签
        for subject in ["s1", "s2", "s3"]:
            subject_label_dir = os.path.join(TRAIN_LABEL_DIR, f"{subject}_label")
            
            if not os.path.exists(subject_label_dir):
                print(f"⚠️ 跳过不存在的目录: {subject_label_dir}")
                continue
            
            # 遍历任务目录
            for task_dir in os.listdir(subject_label_dir):
                label_task_path = os.path.join(subject_label_dir, task_dir)
                
                if not os.path.isdir(label_task_path):
                    continue
                
                # 收集标签文件
                for label_file in os.listdir(label_task_path):
                    if label_file.endswith('.npy'):
                        label_path = os.path.join(label_task_path, label_file)
                        label_files.append(label_path)
        
        print(f"📁 找到 {len(label_files)} 个训练标签文件")
        return label_files
    
    def compute_transition_counts(self, label_files: List[str]) -> Tuple[Dict, Dict, Dict]:
        """
        计算转移频次
        
        Returns:
            transitions: 转移计数字典 {(i, j): count}
            totals: 每个动作的总出现次数 {i: count}
            stats: 统计信息
        """
        print("🔄 开始计算转移频次...")
        
        transitions = defaultdict(int)
        totals = defaultdict(int)
        
        stats = {
            'total_transitions': 0,
            'unique_transitions': 0,
            'processed_files': 0,
            'skipped_files': 0,
            'total_frames': 0
        }
        
        # 遍历所有标签文件
        for i, label_path in enumerate(label_files):
            try:
                # 加载标签序列
                labels = np.load(label_path)  # (N,)
                
                # 计算相邻转移
                for j in range(len(labels) - 1):
                    u, v = int(labels[j]), int(labels[j + 1])
                    transitions[(u, v)] += 1
                    totals[u] += 1
                    stats['total_transitions'] += 1
                
                stats['processed_files'] += 1
                stats['total_frames'] += len(labels)
                
                if (i + 1) % 50 == 0:
                    print(f"   处理进度: {i + 1}/{len(label_files)}")
                
            except Exception as e:
                print(f"⚠️ 处理文件失败 {label_path}: {e}")
                stats['skipped_files'] += 1
                continue
        
        stats['unique_transitions'] = len(transitions)
        
        print(f"✅ 转移频次计算完成:")
        print(f"   - 总转移数: {stats['total_transitions']}")
        print(f"   - 唯一转移数: {stats['unique_transitions']}")
        print(f"   - 处理文件数: {stats['processed_files']}")
        
        return dict(transitions), dict(totals), stats
    
    def compute_edge_weights(self, transitions: Dict, totals: Dict) -> Tuple[torch.Tensor, Dict, Dict]:
        """
        计算边权重矩阵
        
        根据IDEA.md公式：
        W_ij^0 = log(P(n_j|n_i)) = log((C_ij + α) / (Σ_k(C_ik + α)))
        
        Returns:
            W0: 边权重矩阵 (M, M)
            prob_matrix: 条件概率矩阵
            count_matrix: 原始计数矩阵
        """
        print("🔄 计算边权重矩阵...")
        
        # 初始化矩阵
        W0 = torch.zeros((self.M, self.M), dtype=torch.float32)
        prob_matrix = torch.zeros((self.M, self.M), dtype=torch.float32)
        count_matrix = torch.zeros((self.M, self.M), dtype=torch.float32)
        
        # 填充计数矩阵
        for (i, j), count in transitions.items():
            if 0 <= i < self.M and 0 <= j < self.M:
                count_matrix[i, j] = count
        
        # 计算条件概率和边权重
        for i in range(self.M):
            total_i = totals.get(i, 0)
            
            for j in range(self.M):
                count_ij = transitions.get((i, j), 0)
                
                # 平滑后的条件概率
                prob_ij = (count_ij + self.alpha) / (total_i + self.M * self.alpha)
                prob_matrix[i, j] = prob_ij
                
                # 边权重（对数概率）
                W0[i, j] = torch.log(torch.tensor(prob_ij))
        
        print(f"✅ 边权重矩阵计算完成")
        
        return W0, prob_matrix, count_matrix

    def save_results(self, W0: torch.Tensor, prob_matrix: torch.Tensor,
                    count_matrix: torch.Tensor, transitions: Dict, totals: Dict, stats: Dict):
        """保存训练结果"""
        print("💾 保存训练结果...")

        # 1. 保存模型参数
        model_path = os.path.join(MODEL_PARAMS_DIR, f"edge_weights_{TIMESTAMP_ID}.pt")
        torch.save(W0, model_path)
        print(f"   ✅ 边权重已保存: {model_path}")

        # 2. 保存CSV格式（便于人工查看）
        csv_path = os.path.join(MODEL_PARAMS_DIR, f"edge_weights_{TIMESTAMP_ID}.csv")
        action_names = [self.label_map.get(str(i), f"Action_{i}") for i in range(self.M)]

        df = pd.DataFrame(W0.numpy(), index=action_names, columns=action_names)
        df.to_csv(csv_path)
        print(f"   ✅ 边权重CSV已保存: {csv_path}")

        # 3. 保存原始数据
        raw_data = {
            'edge_weights': W0,
            'probability_matrix': prob_matrix,
            'count_matrix': count_matrix,
            'transitions': transitions,
            'totals': totals,
            'statistics': stats,
            'metadata': {
                'timestamp_id': TIMESTAMP_ID,
                'num_classes': self.M,
                'smoothing_alpha': self.alpha,
                'label_map': self.label_map
            }
        }

        raw_data_path = os.path.join(RAW_DATA_DIR, f"edge_weight_raw_data_{TIMESTAMP_ID}.pt")
        torch.save(raw_data, raw_data_path)

        # 保存统计信息为JSON
        stats_path = os.path.join(RAW_DATA_DIR, f"edge_weight_statistics_{TIMESTAMP_ID}.json")
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

        print(f"   ✅ 原始数据已保存: {raw_data_path}")
        print(f"   ✅ 统计信息已保存: {stats_path}")

    def generate_visualizations(self, W0: torch.Tensor, prob_matrix: torch.Tensor,
                              count_matrix: torch.Tensor, stats: Dict):
        """生成可视化图表"""
        print("📊 生成可视化图表...")

        action_names = [self.label_map.get(str(i), f"Action_{i}") for i in range(self.M)]

        # 1. 原始转移频次矩阵热力图
        plt.figure(figsize=(14, 12))

        # 只显示有数据的类别
        nonzero_mask = (count_matrix.sum(dim=1) > 0) | (count_matrix.sum(dim=0) > 0)
        if nonzero_mask.any():
            valid_indices = torch.where(nonzero_mask)[0]
            valid_count_matrix = count_matrix[valid_indices][:, valid_indices]
            valid_names = [action_names[i] for i in valid_indices]

            sns.heatmap(valid_count_matrix.numpy(),
                       xticklabels=valid_names,
                       yticklabels=valid_names,
                       annot=True, fmt='.0f',
                       cmap='Blues')
            plt.title('原始转移频次矩阵')
            plt.xlabel('目标动作')
            plt.ylabel('源动作')
            plt.tight_layout()

            count_heatmap_path = os.path.join(VISUALIZATION_DIR, f"transition_counts_{TIMESTAMP_ID}.png")
            plt.savefig(count_heatmap_path, dpi=300, bbox_inches='tight')
            plt.close()

        # 2. 条件概率矩阵热力图
        plt.figure(figsize=(14, 12))

        if nonzero_mask.any():
            valid_prob_matrix = prob_matrix[valid_indices][:, valid_indices]

            sns.heatmap(valid_prob_matrix.numpy(),
                       xticklabels=valid_names,
                       yticklabels=valid_names,
                       annot=True, fmt='.3f',
                       cmap='viridis')
            plt.title('条件概率矩阵 P(n_j|n_i)')
            plt.xlabel('目标动作')
            plt.ylabel('源动作')
            plt.tight_layout()

            prob_heatmap_path = os.path.join(VISUALIZATION_DIR, f"transition_probabilities_{TIMESTAMP_ID}.png")
            plt.savefig(prob_heatmap_path, dpi=300, bbox_inches='tight')
            plt.close()

        # 3. 边权重矩阵热力图
        plt.figure(figsize=(14, 12))

        if nonzero_mask.any():
            valid_W0 = W0[valid_indices][:, valid_indices]

            sns.heatmap(valid_W0.numpy(),
                       xticklabels=valid_names,
                       yticklabels=valid_names,
                       annot=True, fmt='.2f',
                       cmap='RdBu_r', center=0)
            plt.title('边权重矩阵 W₀ (log概率)')
            plt.xlabel('目标动作')
            plt.ylabel('源动作')
            plt.tight_layout()

            weight_heatmap_path = os.path.join(VISUALIZATION_DIR, f"edge_weights_{TIMESTAMP_ID}.png")
            plt.savefig(weight_heatmap_path, dpi=300, bbox_inches='tight')
            plt.close()

        print(f"   ✅ 可视化图表已保存到: {VISUALIZATION_DIR}")

    def run_training(self):
        """执行完整的训练流程"""
        print("=" * 80)
        print("🚀 任务图边权训练开始")
        print("=" * 80)

        # 1. 查找训练标签文件
        label_files = self.find_training_label_files()
        if not label_files:
            print("❌ 未找到训练标签文件!")
            return

        # 2. 计算转移频次
        transitions, totals, stats = self.compute_transition_counts(label_files)

        # 3. 计算边权重
        W0, prob_matrix, count_matrix = self.compute_edge_weights(transitions, totals)

        # 4. 保存结果
        self.save_results(W0, prob_matrix, count_matrix, transitions, totals, stats)

        # 5. 生成可视化
        self.generate_visualizations(W0, prob_matrix, count_matrix, stats)

        print("=" * 80)
        print("✅ 任务图边权训练完成!")
        print(f"📁 输出目录: {OUTPUT_BASE}")
        print(f"🏷️ 时间戳ID: {TIMESTAMP_ID}")
        print("=" * 80)


def main():
    """主函数"""
    trainer = EdgeWeightTrainer(alpha=1.0)
    trainer.run_training()


if __name__ == "__main__":
    main()
