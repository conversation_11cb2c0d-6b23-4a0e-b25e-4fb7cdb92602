"""
离线统计计算模块

计算并保存动作原型(prototypes)和初始图权重(W0)
"""

import torch
import numpy as np
from collections import defaultdict
import os
from typing import Any


def compute_and_save_stats(dataset: Any, M: int, save_dir: str):
    """
    计算并保存统计数据
    
    Args:
        dataset: 一个配置为 'train' split 的 CerealsDataset 实例
        M: 动作类别总数
        save_dir: 保存统计结果的目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 计算原型 (Prototypes)
    # 鲁棒地获取特征维度D，避免首个样本为空的错误
    D = -1
    for V_seq_sample, _ in dataset:
        if V_seq_sample is not None and len(V_seq_sample) > 0:
            D = V_seq_sample.shape[1]
            break
    
    if D == -1:
        raise ValueError("Could not determine feature dimension from the dataset.")

    proto_sum = torch.zeros((M, D), dtype=torch.float32)
    proto_count = torch.zeros(M, dtype=torch.float32)
    
    print(f"Computing prototypes with D={D}, M={M}...")
    
    # 遍历数据集计算每个动作类别的特征均值
    for i, (V_seq, y_seq) in enumerate(dataset):
        if V_seq is None: 
            continue
            
        for n_k in range(M):
            mask = (y_seq == n_k)
            if mask.any():
                proto_sum[n_k] += V_seq[mask].sum(dim=0)
                proto_count[n_k] += mask.sum()
        
        if (i + 1) % 100 == 0:
            print(f"Processed {i + 1} samples...")
    
    # 计算原型，对于没有数据的动作类别使用零向量
    prototypes = torch.zeros((M, D), dtype=torch.float32)
    for n_k in range(M):
        if proto_count[n_k] > 0:
            prototypes[n_k] = proto_sum[n_k] / proto_count[n_k]
        # 没有数据的动作类别保持零向量
    
    print(f"Computed prototypes for {(proto_count > 1).sum().item()} action classes")
    
    # 2. 计算初始图权重 (W0)
    print("Computing initial graph weights...")
    
    transitions = defaultdict(int)
    totals = defaultdict(int)
    
    for i, (_, y_seq) in enumerate(dataset):
        if y_seq is None: 
            continue
            
        for j in range(len(y_seq) - 1):
            u, v = y_seq[j].item(), y_seq[j+1].item()
            transitions[(u, v)] += 1
            totals[u] += 1
        
        if (i + 1) % 100 == 0:
            print(f"Processed {i + 1} samples for transitions...")
            
    W0 = torch.zeros((M, M), dtype=torch.float32)
    epsilon = 1e-9
    
    for i in range(M):
        total = totals.get(i, 0)
        for j in range(M):
            count = transitions.get((i, j), 0)
            # 计算平滑后的概率并转换为logits
            prob = (count + epsilon) / (total + M * epsilon)
            W0[i, j] = torch.log(torch.tensor(prob))

    print(f"Computed transition matrix with {len(transitions)} unique transitions")

    # 3. 保存结果
    prototypes_path = os.path.join(save_dir, "prototypes.pt")
    w0_path = os.path.join(save_dir, "W0.pt")
    
    torch.save(prototypes, prototypes_path)
    torch.save(W0, w0_path)
    
    print(f"Prototypes (shape: {prototypes.shape}) saved to {prototypes_path}")
    print(f"W0 (shape: {W0.shape}) saved to {w0_path}")
    
    # 保存一些统计信息
    stats_info = {
        'feature_dim': D,
        'num_actions': M,
        'num_transitions': len(transitions),
        'actions_with_data': (proto_count > 1).sum().item(),
        'total_frames': sum(proto_count).item()
    }
    
    stats_path = os.path.join(save_dir, "stats_info.txt")
    with open(stats_path, 'w') as f:
        for key, value in stats_info.items():
            f.write(f"{key}: {value}\n")
    
    print(f"Statistics info saved to {stats_path}")
    return prototypes, W0


def load_stats(stats_dir: str):
    """
    加载预计算的统计数据
    
    Args:
        stats_dir: 统计数据目录
        
    Returns:
        (prototypes, W0) 元组
    """
    prototypes_path = os.path.join(stats_dir, "prototypes.pt")
    w0_path = os.path.join(stats_dir, "W0.pt")
    
    if not os.path.exists(prototypes_path):
        raise FileNotFoundError(f"Prototypes file not found: {prototypes_path}")
    if not os.path.exists(w0_path):
        raise FileNotFoundError(f"W0 file not found: {w0_path}")
    
    prototypes = torch.load(prototypes_path)
    W0 = torch.load(w0_path)
    
    print(f"Loaded prototypes: {prototypes.shape}")
    print(f"Loaded W0: {W0.shape}")
    
    return prototypes, W0


def verify_stats(prototypes: torch.Tensor, W0: torch.Tensor, expected_D: int, expected_M: int):
    """
    验证统计数据的形状和有效性
    
    Args:
        prototypes: 原型矩阵
        W0: 初始权重矩阵
        expected_D: 期望的特征维度
        expected_M: 期望的动作类别数
    """
    assert prototypes.shape == (expected_M, expected_D), f"Prototypes shape mismatch: {prototypes.shape} vs ({expected_M}, {expected_D})"
    assert W0.shape == (expected_M, expected_M), f"W0 shape mismatch: {W0.shape} vs ({expected_M}, {expected_M})"
    
    # 检查是否有NaN或Inf
    assert not torch.isnan(prototypes).any(), "Prototypes contain NaN values"
    assert not torch.isinf(prototypes).any(), "Prototypes contain Inf values"
    assert not torch.isnan(W0).any(), "W0 contains NaN values"
    assert not torch.isinf(W0).any(), "W0 contains Inf values"
    
    print("Statistics verification passed")


if __name__ == "__main__":
    """
    支持 python -m src.utils.stats_computer 命令
    用于离线计算和保存统计数据
    """
    import sys
    sys.path.insert(0, '.')

    from src.data.datamodule import CerealsDataset
    from src.pipeline.train import ACTION_MAP

    # 使用默认配置
    data_root = "/data2/syd_data/Breakfast_Data"
    M = 48
    save_dir = "./stats"

    print("Creating dataset for statistics computation...")
    dataset = CerealsDataset(data_root, "train", ACTION_MAP)
    print(f"Dataset created with {len(dataset)} samples")

    if len(dataset) == 0:
        raise ValueError("数据集为空，请检查数据路径配置。")

    # 计算并保存统计数据
    prototypes, W0 = compute_and_save_stats(dataset, M, save_dir)

    # 验证统计数据
    D = prototypes.shape[1]
    verify_stats(prototypes, W0, D, M)

    print("Statistics computation completed successfully!")
