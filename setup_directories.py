#!/usr/bin/env python3
"""
目录结构设置脚本 - setup_directories.py
根据IDEA.md第5章规范创建完整的输出目录结构

主要功能：
1. 创建所有必要的输出目录
2. 验证目录结构的完整性
3. 生成目录结构报告
"""

import os
from pathlib import Path
from datetime import datetime

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs")

# 根据IDEA.md第5章定义的目录结构
DIRECTORY_STRUCTURE = {
    "Action_Prototype": {
        "Model_parameters": "动作原型的参数存储",
        "Raw_data": "动作原型训练过程的详细数据",
        "Visualization": "原型特征的训练过程可视化图"
    },
    "Edge_Weight": {
        "Model_parameters": "初始图权重存储",
        "Raw_data": "初始图训练过程的详细数据",
        "Visualization": "边权的训练过程可视化图表"
    },
    "Static": {
        "Model_parameters": "静态任务图的模型参数",
        "Raw_data": "静态任务图训练过程的详细数据",
        "Visualization": "静态任务图的训练过程可视化图表"
    },
    "Dynamic": {
        "Model_parameters": "动态任务图的模型参数",
        "Raw_data": "动态任务图训练过程的详细数据",
        "Visualization": "动态任务图的训练过程可视化图表"
    },
    "Evaluation_Result": {
        "": "推理测试的存储路径"
    }
}


class DirectorySetup:
    """目录结构设置器"""
    
    def __init__(self):
        self.created_dirs = []
        self.existing_dirs = []
        
        print("📁 目录结构设置器初始化")
        print(f"   - 数据根目录: {DATA_ROOT}")
        print(f"   - 输出基目录: {OUTPUT_BASE}")
    
    def create_directory_structure(self):
        """创建完整的目录结构"""
        print("\n🔄 创建目录结构...")
        
        # 创建输出基目录
        if not os.path.exists(OUTPUT_BASE):
            os.makedirs(OUTPUT_BASE)
            self.created_dirs.append(OUTPUT_BASE)
            print(f"   ✅ 创建基目录: {OUTPUT_BASE}")
        else:
            self.existing_dirs.append(OUTPUT_BASE)
        
        # 创建各模块的目录结构
        for module_name, subdirs in DIRECTORY_STRUCTURE.items():
            module_path = os.path.join(OUTPUT_BASE, module_name)
            
            # 创建模块目录
            if not os.path.exists(module_path):
                os.makedirs(module_path)
                self.created_dirs.append(module_path)
                print(f"   ✅ 创建模块目录: {module_name}")
            else:
                self.existing_dirs.append(module_path)
            
            # 创建子目录
            for subdir_name, description in subdirs.items():
                if subdir_name:  # 非空子目录名
                    subdir_path = os.path.join(module_path, subdir_name)
                    
                    if not os.path.exists(subdir_path):
                        os.makedirs(subdir_path)
                        self.created_dirs.append(subdir_path)
                        print(f"     ✅ 创建子目录: {module_name}/{subdir_name}")
                    else:
                        self.existing_dirs.append(subdir_path)
        
        print(f"\n📊 目录创建统计:")
        print(f"   - 新创建目录: {len(self.created_dirs)}")
        print(f"   - 已存在目录: {len(self.existing_dirs)}")
    
    def verify_directory_structure(self) -> bool:
        """验证目录结构的完整性"""
        print("\n🔍 验证目录结构完整性...")
        
        missing_dirs = []
        
        # 检查输出基目录
        if not os.path.exists(OUTPUT_BASE):
            missing_dirs.append(OUTPUT_BASE)
        
        # 检查各模块目录
        for module_name, subdirs in DIRECTORY_STRUCTURE.items():
            module_path = os.path.join(OUTPUT_BASE, module_name)
            
            if not os.path.exists(module_path):
                missing_dirs.append(module_path)
                continue
            
            # 检查子目录
            for subdir_name, description in subdirs.items():
                if subdir_name:  # 非空子目录名
                    subdir_path = os.path.join(module_path, subdir_name)
                    
                    if not os.path.exists(subdir_path):
                        missing_dirs.append(subdir_path)
        
        if missing_dirs:
            print(f"   ❌ 发现 {len(missing_dirs)} 个缺失目录:")
            for missing_dir in missing_dirs:
                print(f"      - {missing_dir}")
            return False
        else:
            print("   ✅ 目录结构完整")
            return True
    
    def generate_structure_report(self):
        """生成目录结构报告"""
        print("\n📋 生成目录结构报告...")
        
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        report_path = os.path.join(OUTPUT_BASE, f"directory_structure_report_{timestamp}.txt")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("目录结构报告\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据根目录: {DATA_ROOT}\n")
            f.write(f"输出基目录: {OUTPUT_BASE}\n\n")
            
            f.write("目录结构 (根据IDEA.md第5章规范):\n")
            f.write("-" * 50 + "\n\n")
            
            # 写入目录树结构
            f.write("Outputs/\n")
            for module_name, subdirs in DIRECTORY_STRUCTURE.items():
                f.write(f"├── {module_name}/\n")
                
                subdir_list = [(k, v) for k, v in subdirs.items() if k]
                for i, (subdir_name, description) in enumerate(subdir_list):
                    is_last = (i == len(subdir_list) - 1)
                    prefix = "└──" if is_last else "├──"
                    f.write(f"│   {prefix} {subdir_name}/  # {description}\n")
                
                if not subdir_list and subdirs.get("", ""):
                    f.write(f"│   # {subdirs['']}\n")
                f.write("│\n")
            
            f.write("\n创建统计:\n")
            f.write("-" * 20 + "\n")
            f.write(f"新创建目录: {len(self.created_dirs)}\n")
            f.write(f"已存在目录: {len(self.existing_dirs)}\n")
            
            if self.created_dirs:
                f.write("\n新创建的目录:\n")
                for created_dir in self.created_dirs:
                    f.write(f"  - {created_dir}\n")
        
        print(f"   ✅ 报告已保存: {report_path}")
        return report_path
    
    def create_readme_files(self):
        """在各目录中创建README文件"""
        print("\n📝 创建README文件...")
        
        readme_content_template = """# {module_name} - {subdir_name}

## 目录说明

{description}

## 文件命名规范

所有文件应遵循以下命名格式：
```
{{基准名}}_{{时间戳ID}}.{{后缀}}
```

其中：
- **基准名**: 描述文件内容的核心名称
- **时间戳ID**: YYYYMMDD-HHMMSS 格式的时间戳
- **后缀**: 文件扩展名 (.pt, .csv, .png 等)

## 示例文件名

- `action_prototypes_20231027-153005.pt`
- `training_curves_20231027-153005.png`
- `evaluation_summary_20231027-153005.csv`

---
*此文件由 setup_directories.py 自动生成*
"""
        
        created_readmes = 0
        
        for module_name, subdirs in DIRECTORY_STRUCTURE.items():
            for subdir_name, description in subdirs.items():
                if subdir_name:  # 非空子目录名
                    subdir_path = os.path.join(OUTPUT_BASE, module_name, subdir_name)
                    readme_path = os.path.join(subdir_path, "README.md")
                    
                    if not os.path.exists(readme_path):
                        content = readme_content_template.format(
                            module_name=module_name,
                            subdir_name=subdir_name,
                            description=description
                        )
                        
                        with open(readme_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        created_readmes += 1
        
        print(f"   ✅ 创建了 {created_readmes} 个README文件")
    
    def run_setup(self):
        """执行完整的目录设置流程"""
        print("=" * 80)
        print("🚀 目录结构设置开始")
        print("=" * 80)
        
        # 1. 创建目录结构
        self.create_directory_structure()
        
        # 2. 验证目录结构
        is_complete = self.verify_directory_structure()
        
        # 3. 生成报告
        report_path = self.generate_structure_report()
        
        # 4. 创建README文件
        self.create_readme_files()
        
        print("=" * 80)
        if is_complete:
            print("✅ 目录结构设置完成!")
        else:
            print("⚠️ 目录结构设置完成，但存在缺失目录")
        print(f"📁 输出基目录: {OUTPUT_BASE}")
        print(f"📋 结构报告: {report_path}")
        print("=" * 80)


def main():
    """主函数"""
    setup = DirectorySetup()
    setup.run_setup()


if __name__ == "__main__":
    main()
