#!/usr/bin/env python3
"""
实验总控脚本 - run_experiment.py
根据IDEA.md规范按顺序执行完整的实验流程

执行顺序：
1. 数据预处理 (txt_to_npy.py)
2. 训练动作原型 (Train/Train_Action_Prototype.py)
3. 训练边权重 (Train/Train_Edge_Weight.py)
4. 训练静态模型 (Train/Train_Static_Model.py)
5. 训练动态模型 (Train/Train_Dynamic_Model.py)
6. 测试静态模型 (Test/Test_Static.py)
7. 测试动态模型 (Test/Test_Dynamic.py)
8. 对比分析 (Test/Test_Static_vs_Dynamic.py)
"""

import sys
import subprocess
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# 全局配置
PROJECT_ROOT = Path(__file__).parent
PYTHON_EXECUTABLE = sys.executable

# 实验步骤配置
EXPERIMENT_STEPS = [
    {
        "name": "目录结构设置",
        "script": "setup_directories.py",
        "description": "创建符合IDEA.md规范的输出目录结构",
        "required": True
    },
    {
        "name": "数据预处理",
        "script": "txt_to_npy.py",
        "description": "将原始txt数据转换为npy格式，实现多视角融合",
        "required": True
    },
    {
        "name": "训练动作原型",
        "script": "Train/Train_Action_Prototype.py",
        "description": "计算各动作类别的原型特征",
        "required": True
    },
    {
        "name": "训练边权重",
        "script": "Train/Train_Edge_Weight.py",
        "description": "统计转移频次并计算初始边权重",
        "required": True
    },
    {
        "name": "训练静态模型",
        "script": "Train/Train_Static_Model.py",
        "description": "训练静态任务图模型",
        "required": True
    },
    {
        "name": "训练动态模型",
        "script": "Train/Train_Dynamic_Model.py",
        "description": "训练动态任务图模型",
        "required": True
    },
    {
        "name": "测试静态模型",
        "script": "Test/Test_Static.py",
        "description": "在测试集上评估静态模型",
        "required": True
    },
    {
        "name": "测试动态模型",
        "script": "Test/Test_Dynamic.py",
        "description": "在测试集上评估动态模型",
        "required": True
    },
    {
        "name": "对比分析",
        "script": "Test/Test_Static_vs_Dynamic.py",
        "description": "静态vs动态模型的Bootstrap置信区间对比",
        "required": True
    }
]


class ExperimentRunner:
    """实验运行器"""
    
    def __init__(self, start_from: Optional[str] = None, skip_steps: Optional[List[str]] = None):
        """
        Args:
            start_from: 从指定步骤开始执行（步骤名称）
            skip_steps: 跳过的步骤列表
        """
        self.start_from = start_from
        self.skip_steps = skip_steps or []
        self.start_time = datetime.now()
        self.step_results = []
        
        print("🚀 实验总控脚本初始化")
        print(f"   - 项目根目录: {PROJECT_ROOT}")
        print(f"   - Python解释器: {PYTHON_EXECUTABLE}")
        print(f"   - 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if self.start_from:
            print(f"   - 从步骤开始: {self.start_from}")
        if self.skip_steps:
            print(f"   - 跳过步骤: {', '.join(self.skip_steps)}")
    
    def run_script(self, script_path: str, step_name: str) -> Dict:
        """
        运行单个脚本
        
        Returns:
            执行结果字典
        """
        full_script_path = PROJECT_ROOT / script_path
        
        if not full_script_path.exists():
            return {
                "success": False,
                "error": f"脚本文件不存在: {full_script_path}",
                "duration": 0,
                "output": ""
            }
        
        print(f"\n{'='*60}")
        print(f"🔄 执行步骤: {step_name}")
        print(f"📄 脚本路径: {script_path}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            # 执行脚本
            result = subprocess.run(
                [PYTHON_EXECUTABLE, str(full_script_path)],
                cwd=PROJECT_ROOT,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ {step_name} 执行成功 (耗时: {duration:.1f}秒)")
                return {
                    "success": True,
                    "error": None,
                    "duration": duration,
                    "output": result.stdout,
                    "stderr": result.stderr
                }
            else:
                print(f"❌ {step_name} 执行失败 (返回码: {result.returncode})")
                print(f"错误输出: {result.stderr}")
                return {
                    "success": False,
                    "error": f"脚本执行失败，返回码: {result.returncode}",
                    "duration": duration,
                    "output": result.stdout,
                    "stderr": result.stderr
                }
        
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            print(f"⏰ {step_name} 执行超时 (超过1小时)")
            return {
                "success": False,
                "error": "执行超时",
                "duration": duration,
                "output": "",
                "stderr": ""
            }
        
        except Exception as e:
            duration = time.time() - start_time
            print(f"💥 {step_name} 执行异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "duration": duration,
                "output": "",
                "stderr": ""
            }
    
    def should_run_step(self, step_name: str, step_index: int) -> bool:
        """判断是否应该执行某个步骤"""
        # 检查是否在跳过列表中
        if step_name in self.skip_steps:
            return False
        
        # 检查是否已到达开始步骤
        if self.start_from:
            # 找到开始步骤的索引
            start_index = None
            for i, step in enumerate(EXPERIMENT_STEPS):
                if step["name"] == self.start_from:
                    start_index = i
                    break
            
            if start_index is not None and step_index < start_index:
                return False
        
        return True
    
    def generate_report(self):
        """生成实验报告"""
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        # 创建报告
        report_path = PROJECT_ROOT / f"experiment_report_{self.start_time.strftime('%Y%m%d-%H%M%S')}.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("实验执行报告\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总耗时: {total_duration:.1f} 秒 ({total_duration/60:.1f} 分钟)\n\n")
            
            # 统计信息
            successful_steps = sum(1 for result in self.step_results if result["success"])
            failed_steps = len(self.step_results) - successful_steps
            
            f.write(f"执行统计:\n")
            f.write(f"  - 总步骤数: {len(self.step_results)}\n")
            f.write(f"  - 成功步骤: {successful_steps}\n")
            f.write(f"  - 失败步骤: {failed_steps}\n\n")
            
            # 详细结果
            f.write("详细执行结果:\n")
            f.write("-" * 50 + "\n")
            
            for i, result in enumerate(self.step_results):
                status = "✅ 成功" if result["success"] else "❌ 失败"
                f.write(f"\n{i+1}. {result['step_name']} - {status}\n")
                f.write(f"   脚本: {result['script_path']}\n")
                f.write(f"   耗时: {result['duration']:.1f} 秒\n")
                
                if not result["success"]:
                    f.write(f"   错误: {result['error']}\n")
                    if result.get('stderr'):
                        f.write(f"   错误输出: {result['stderr'][:500]}...\n")
        
        print(f"\n📋 实验报告已保存: {report_path}")
        return report_path

    def run_experiment(self):
        """执行完整的实验流程"""
        print("\n" + "=" * 80)
        print("🧪 开始执行完整实验流程")
        print("=" * 80)

        # 显示实验步骤概览
        print("\n📋 实验步骤概览:")
        for i, step in enumerate(EXPERIMENT_STEPS):
            status = "⏭️ 跳过" if step["name"] in self.skip_steps else "🔄 执行"
            if self.start_from:
                start_index = None
                for j, s in enumerate(EXPERIMENT_STEPS):
                    if s["name"] == self.start_from:
                        start_index = j
                        break
                if start_index is not None and i < start_index:
                    status = "⏭️ 跳过"

            print(f"   {i+1}. {step['name']} - {status}")
            print(f"      {step['description']}")

        print(f"\n🚀 开始执行...")

        # 执行各个步骤
        for i, step in enumerate(EXPERIMENT_STEPS):
            step_name = step["name"]
            script_path = step["script"]

            # 判断是否应该执行此步骤
            if not self.should_run_step(step_name, i):
                print(f"\n⏭️ 跳过步骤: {step_name}")
                continue

            # 执行步骤
            result = self.run_script(script_path, step_name)

            # 记录结果
            step_result = {
                "step_name": step_name,
                "script_path": script_path,
                "success": result["success"],
                "error": result["error"],
                "duration": result["duration"]
            }

            if result.get("stderr"):
                step_result["stderr"] = result["stderr"]

            self.step_results.append(step_result)

            # 如果是必需步骤且失败了，询问是否继续
            if not result["success"] and step.get("required", True):
                print(f"\n⚠️ 必需步骤 '{step_name}' 执行失败!")
                print(f"错误信息: {result['error']}")

                user_input = input("是否继续执行后续步骤? (y/N): ").strip().lower()
                if user_input not in ['y', 'yes']:
                    print("🛑 用户选择停止执行")
                    break

        # 生成报告
        print("\n📊 生成实验报告...")
        report_path = self.generate_report()

        # 显示总结
        successful_steps = sum(1 for result in self.step_results if result["success"])
        total_steps = len(self.step_results)

        print("\n" + "=" * 80)
        print("🏁 实验执行完成")
        print("=" * 80)
        print(f"📊 执行统计: {successful_steps}/{total_steps} 步骤成功")
        print(f"⏱️ 总耗时: {(datetime.now() - self.start_time).total_seconds():.1f} 秒")
        print(f"📋 详细报告: {report_path}")

        if successful_steps == total_steps:
            print("🎉 所有步骤执行成功!")
        else:
            print("⚠️ 部分步骤执行失败，请查看报告了解详情")

        print("=" * 80)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="实验总控脚本")
    parser.add_argument("--start-from", type=str, help="从指定步骤开始执行")
    parser.add_argument("--skip", type=str, nargs="+", help="跳过指定步骤")
    parser.add_argument("--list-steps", action="store_true", help="列出所有步骤")

    args = parser.parse_args()

    # 列出步骤
    if args.list_steps:
        print("📋 可用的实验步骤:")
        for i, step in enumerate(EXPERIMENT_STEPS):
            print(f"   {i+1}. {step['name']}")
            print(f"      脚本: {step['script']}")
            print(f"      描述: {step['description']}")
            print(f"      必需: {'是' if step.get('required', True) else '否'}")
            print()
        return

    # 验证参数
    if args.start_from:
        valid_steps = [step["name"] for step in EXPERIMENT_STEPS]
        if args.start_from not in valid_steps:
            print(f"❌ 无效的起始步骤: {args.start_from}")
            print(f"可用步骤: {', '.join(valid_steps)}")
            return

    if args.skip:
        valid_steps = [step["name"] for step in EXPERIMENT_STEPS]
        for skip_step in args.skip:
            if skip_step not in valid_steps:
                print(f"❌ 无效的跳过步骤: {skip_step}")
                print(f"可用步骤: {', '.join(valid_steps)}")
                return

    # 创建并运行实验
    runner = ExperimentRunner(start_from=args.start_from, skip_steps=args.skip)
    runner.run_experiment()


if __name__ == "__main__":
    main()
