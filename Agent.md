# 动态任务图谱差分更新实验完整代码框架

## 项目结构

```
/data2/syd_data/Breakfast_Data/Code/
├── config/
│   ├── config.yaml                 # 全局配置文件
│   └── hyperparams.yaml           # 超参数配置
├── utils/
│   ├── __init__.py
│   ├── data_loader.py             # 数据加载工具
│   ├── evaluation_metrics.py      # 评估指标计算
│   ├── visualization.py           # 可视化工具
│   └── file_utils.py              # 文件操作工具
├── models/
│   ├── __init__.py
│   ├── mlp_diff.py                # MLP_diff网络定义
│   └── base_model.py              # 基础模型类
├── Train/
│   ├── Train_Action_Prototype.py   # 1. 原型特征训练
│   ├── Train_Edge_Weight.py        # 2. 任务图边权训练
│   ├── Train_Static_Model.py       # 3. 静态任务图训练
│   └── Train_Dynamic_Model.py      # 4. 动态任务图训练
├── Test/
│   ├── Test_Static.py              # 静态模型测试
│   ├── Test_Dynamic.py             # 动态模型测试
│   └── Test_Static_vs_Dynamic.py   # 对比测试
├── txt_to_npy.py                   # 数据预处理脚本
└── run_experiment.py               # 完整实验运行脚本
```

## 1. 全局配置文件

### config/config.yaml

```yaml
# 数据路径配置
data:
  raw_data_path: "/data2/syd_data/Breakfast_Data/breakfast_data"
  raw_label_path: "/data2/syd_data/Breakfast_Data/segmentation_coarse"
  npy_data_path: "/data2/syd_data/Breakfast_Data/breakfast_data_npy"
  npy_label_path: "/data2/syd_data/Breakfast_Data/segmentation_coarse_npy"
  label_map_path: "/data2/syd_data/Breakfast_Data/label_map.json"

# 输出路径配置
output:
  base_path: "/data2/syd_data/Breakfast_Data/Outputs"
  action_prototype: "Action_Prototype"
  edge_weight: "Edge_Weight"
  static: "Static"
  dynamic: "Dynamic"
  evaluation: "Evaluation_Result"

# 训练配置
training:
  train_splits: ["s1", "s2", "s3"]
  test_splits: ["s4"]
  max_epochs: 10
  batch_size: 32
  learning_rate: 0.001
  random_seed: 42
  early_stopping: false

# 模型配置
model:
  feature_dim: 64
  mlp_hidden_dims: [512, 256]
  activation: "relu"
  laplace_smoothing_alpha: 1.0

# 评估配置
evaluation:
  iou_thresholds: [0.5, 0.75, 0.9]
  bootstrap_samples: 500
  confidence_interval: 0.95

# 可视化配置
visualization:
  style: "whitegrid"
  dpi: 300
  figsize: [10, 6]
```

### config/hyperparams.yaml

```yaml
# 超参数搜索空间（如需要）
mlp_diff:
  hidden_dims:
    - [256, 128]
    - [512, 256]
    - [1024, 512]
  learning_rate: [0.001, 0.0005, 0.0001]
  dropout: [0.0, 0.1, 0.2]
```

## 2. 核心工具类

### utils/data_loader.py

```python
"""数据加载和预处理工具"""
import os
import numpy as np
import json
from typing import Dict, List, Tuple, Optional
from pathlib import Path

class BreakfastDataLoader:
    """Breakfast数据集加载器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.label_map = self._load_label_map()
        self.num_classes = len(self.label_map)
    
    def _load_label_map(self) -> Dict[str, str]:
        """加载动作标签映射"""
        pass
    
    def load_npy_data(self, split: str, task: str) -> Tuple[np.ndarray, np.ndarray]:
        """加载.npy格式的特征和标签数据"""
        pass
    
    def get_transition_samples(self, features: np.ndarray, labels: np.ndarray) -> List[Dict]:
        """生成动作转换样本（仅在段边界处）"""
        pass
    
    def get_all_training_data(self) -> Tuple[List[np.ndarray], List[int], List[int]]:
        """获取所有训练数据的转换样本"""
        pass
    
    def get_all_test_data(self) -> Dict[str, Tuple[np.ndarray, np.ndarray]]:
        """获取所有测试数据"""
        pass

class MultiViewFusion:
    """多视角数据融合器"""
    
    @staticmethod
    def fuse_views(view_features: List[np.ndarray]) -> np.ndarray:
        """融合多个视角的特征（按文档要求）"""
        pass
    
    @staticmethod
    def sync_frames(view_features: List[np.ndarray]) -> List[np.ndarray]:
        """同步不同视角的帧"""
        pass
```

### utils/evaluation_metrics.py

```python
"""评估指标计算工具"""
import numpy as np
from typing import List, Dict, Tuple
from scipy.stats import bootstrap

class EvaluationMetrics:
    """评估指标计算器"""
    
    def __init__(self, iou_thresholds: List[float] = [0.5, 0.75, 0.9]):
        self.iou_thresholds = iou_thresholds
    
    def compute_temporal_iou(self, pred_segments: List[Tuple], 
                           gt_segments: List[Tuple], 
                           threshold: float) -> float:
        """计算时序段级IoU"""
        pass
    
    def compute_frame_accuracy(self, pred_labels: np.ndarray, 
                              gt_labels: np.ndarray) -> float:
        """计算帧级准确率"""
        pass
    
    def compute_edit_distance(self, pred_sequence: List[str], 
                             gt_sequence: List[str]) -> float:
        """计算归一化编辑距离"""
        pass
    
    def compute_action_coverage(self, pred_actions: set, 
                               gt_actions: set) -> float:
        """计算动作覆盖率"""
        pass
    
    def bootstrap_confidence_interval(self, data: List, 
                                    metric_func: callable, 
                                    n_bootstrap: int = 500, 
                                    confidence: float = 0.95) -> Tuple[float, float, float]:
        """计算Bootstrap置信区间"""
        pass
    
    def evaluate_all_metrics(self, predictions: Dict, 
                           ground_truth: Dict) -> Dict[str, Dict]:
        """计算所有评估指标"""
        pass
```

### utils/visualization.py

```python
"""可视化工具"""
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional

class VisualizationManager:
    """可视化管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        sns.set_theme(style=config['visualization']['style'])
        self.dpi = config['visualization']['dpi']
        self.figsize = config['visualization']['figsize']
    
    def plot_training_curves(self, train_losses: List[float], 
                           val_losses: List[float], 
                           save_path: str):
        """绘制训练损失曲线"""
        pass
    
    def plot_confusion_matrix(self, y_true: np.ndarray, 
                            y_pred: np.ndarray, 
                            class_names: List[str], 
                            save_path: str):
        """绘制混淆矩阵"""
        pass
    
    def plot_transition_matrix(self, transition_matrix: np.ndarray, 
                             action_names: List[str], 
                             save_path: str):
        """绘制转移矩阵热力图"""
        pass
    
    def plot_prototype_statistics(self, prototype_stats: Dict, 
                                save_path: str):
        """绘制原型特征统计图"""
        pass
    
    def plot_comparison_results(self, static_results: Dict, 
                              dynamic_results: Dict, 
                              save_path: str):
        """绘制静态vs动态模型对比图"""
        pass
```

### utils/file_utils.py

```python
"""文件操作工具"""
import os
import json
import pickle
import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

class FileManager:
    """文件管理器"""
    
    def __init__(self, base_output_path: str):
        self.base_output_path = Path(base_output_path)
        self.timestamp_id = self._generate_timestamp_id()
    
    def _generate_timestamp_id(self) -> str:
        """生成时间戳ID (YYYYMMDD-HHMMSS)"""
        return datetime.now().strftime("%Y%m%d-%H%M%S")
    
    def create_output_directories(self):
        """创建所有必要的输出目录"""
        pass
    
    def save_model_parameters(self, model, model_type: str, filename: str):
        """保存模型参数"""
        pass
    
    def save_raw_data(self, data: Any, model_type: str, filename: str):
        """保存原始数据"""
        pass
    
    def save_visualization(self, fig, model_type: str, filename: str):
        """保存可视化图表"""
        pass
    
    def save_evaluation_results(self, results: Dict, filename: str):
        """保存评估结果"""
        pass
    
    def get_timestamped_filename(self, base_name: str, extension: str) -> str:
        """生成带时间戳的文件名"""
        return f"{base_name}_{self.timestamp_id}.{extension}"
```

## 3. 模型定义

### models/base_model.py

```python
"""基础模型类"""
import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Any

class BaseModel(nn.Module, ABC):
    """基础模型抽象类"""
    
    def __init__(self, config: Dict):
        super().__init__()
        self.config = config
    
    @abstractmethod
    def forward(self, *args, **kwargs):
        pass
    
    def save_model(self, filepath: str):
        """保存模型"""
        torch.save(self.state_dict(), filepath)
    
    def load_model(self, filepath: str):
        """加载模型"""
        self.load_state_dict(torch.load(filepath))
```

### models/mlp_diff.py

```python
"""MLP_diff网络定义"""
import torch
import torch.nn as nn
from .base_model import BaseModel
from typing import List

class MLPDiff(BaseModel):
    """差分MLP网络"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        
        input_dim = config['model']['feature_dim']
        hidden_dims = config['model']['mlp_hidden_dims']
        output_dim = self.num_classes  # 需要从数据中获取
        
        layers = []
        prev_dim = input_dim
        
        # 隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.mlp = nn.Sequential(*layers)
    
    def forward(self, diff_features: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.mlp(diff_features)
```

## 4. 数据预处理脚本

### txt_to_npy.py

```python
"""TXT到NPY格式转换脚本"""
import os
import numpy as np
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from utils.data_loader import MultiViewFusion
from utils.file_utils import FileManager

class TxtToNpyConverter:
    """TXT到NPY转换器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.setup_logging()
        self.label_map = self._load_or_create_label_map()
    
    def setup_logging(self):
        """设置日志"""
        log_path = os.path.join(self.config['data']['raw_data_path'], '..', 'Code', 'txt_to_npy.log')
        logging.basicConfig(
            filename=log_path,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def _load_or_create_label_map(self) -> Dict[str, int]:
        """加载或创建标签映射"""
        pass
    
    def convert_features(self, txt_path: str, npy_path: str) -> bool:
        """转换特征文件"""
        pass
    
    def convert_labels(self, txt_path: str, npy_path: str) -> bool:
        """转换标签文件"""
        pass
    
    def validate_conversion(self, txt_data: np.ndarray, npy_data: np.ndarray) -> bool:
        """验证转换的一致性"""
        pass
    
    def convert_all_files(self):
        """转换所有文件"""
        pass
    
    def run(self):
        """运行转换流程"""
        pass

if __name__ == "__main__":
    from utils.file_utils import FileManager
    import yaml
    
    # 加载配置
    with open('config/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # 运行转换
    converter = TxtToNpyConverter(config)
    converter.run()
```

## 5. 训练脚本

### Train/Train_Action_Prototype.py

```python
"""原型特征训练脚本"""
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List
from utils.data_loader import BreakfastDataLoader
from utils.file_utils import FileManager
from utils.visualization import VisualizationManager
import yaml

class ActionPrototypeTrainer:
    """动作原型特征训练器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.data_loader = BreakfastDataLoader(config)
        self.file_manager = FileManager(config['output']['base_path'])
        self.viz_manager = VisualizationManager(config)
        
    def compute_prototypes(self) -> Dict[str, np.ndarray]:
        """计算所有动作类别的原型特征"""
        pass
    
    def compute_statistics(self, prototypes: Dict) -> Dict:
        """计算原型特征统计信息"""
        pass
    
    def save_results(self, prototypes: Dict, statistics: Dict):
        """保存结果"""
        pass
    
    def visualize_results(self, statistics: Dict):
        """可视化结果"""
        pass
    
    def train(self):
        """执行训练流程"""
        print("开始训练动作原型特征...")
        
        # 计算原型
        prototypes = self.compute_prototypes()
        
        # 计算统计信息
        statistics = self.compute_statistics(prototypes)
        
        # 保存结果
        self.save_results(prototypes, statistics)
        
        # 可视化
        self.visualize_results(statistics)
        
        print("动作原型特征训练完成!")

if __name__ == "__main__":
    with open('../config/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    trainer = ActionPrototypeTrainer(config)
    trainer.train()
```

### Train/Train_Edge_Weight.py

```python
"""任务图边权训练脚本"""
import numpy as np
import torch
import pandas as pd
from collections import defaultdict
from typing import Dict, Tuple
from utils.data_loader import BreakfastDataLoader
from utils.file_utils import FileManager
from utils.visualization import VisualizationManager
import yaml

class EdgeWeightTrainer:
    """任务图边权训练器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.data_loader = BreakfastDataLoader(config)
        self.file_manager = FileManager(config['output']['base_path'])
        self.viz_manager = VisualizationManager(config)
        self.alpha = config['model']['laplace_smoothing_alpha']
    
    def compute_transition_counts(self) -> np.ndarray:
        """计算转移频次矩阵C_ij"""
        pass
    
    def compute_transition_probabilities(self, counts: np.ndarray) -> np.ndarray:
        """计算平滑后的条件概率矩阵"""
        pass
    
    def compute_edge_weights(self, probabilities: np.ndarray) -> np.ndarray:
        """计算边权重矩阵W_ij^0"""
        pass
    
    def save_results(self, counts: np.ndarray, probabilities: np.ndarray, weights: np.ndarray):
        """保存结果"""
        pass
    
    def visualize_results(self, counts: np.ndarray, probabilities: np.ndarray, weights: np.ndarray):
        """可视化结果"""
        pass
    
    def train(self):
        """执行训练流程"""
        print("开始训练任务图边权...")
        
        # 计算转移频次
        counts = self.compute_transition_counts()
        
        # 计算条件概率
        probabilities = self.compute_transition_probabilities(counts)
        
        # 计算边权重
        weights = self.compute_edge_weights(probabilities)
        
        # 保存结果
        self.save_results(counts, probabilities, weights)
        
        # 可视化
        self.visualize_results(counts, probabilities, weights)
        
        print("任务图边权训练完成!")

if __name__ == "__main__":
    with open('../config/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    trainer = EdgeWeightTrainer(config)
    trainer.train()
```

### Train/Train_Static_Model.py

```python
"""静态任务图训练脚本"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from typing import Dict, List, Tuple
from utils.data_loader import BreakfastDataLoader
from utils.file_utils import FileManager
from utils.visualization import VisualizationManager
import yaml

class StaticModelDataset(Dataset):
    """静态模型数据集"""
    
    def __init__(self, samples: List[Dict]):
        self.samples = samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        return {
            'current_action': sample['current_action'],
            'next_action': sample['next_action']
        }

class StaticModel(nn.Module):
    """静态任务图模型"""
    
    def __init__(self, num_classes: int, edge_weights: torch.Tensor):
        super().__init__()
        self.edge_weights = nn.Parameter(edge_weights, requires_grad=False)  # 冻结
        self.bias = nn.Parameter(torch.zeros(num_classes))  # 可学习
    
    def forward(self, current_actions: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        pass

class StaticModelTrainer:
    """静态任务图训练器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.data_loader = BreakfastDataLoader(config)
        self.file_manager = FileManager(config['output']['base_path'])
        self.viz_manager = VisualizationManager(config)
        
        # 加载边权重
        self.edge_weights = self._load_edge_weights()
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def _load_edge_weights(self) -> torch.Tensor:
        """加载预训练的边权重"""
        pass
    
    def create_model(self) -> StaticModel:
        """创建静态模型"""
        pass
    
    def train_epoch(self, model: StaticModel, dataloader: DataLoader, 
                   optimizer: optim.Optimizer, criterion: nn.Module) -> float:
        """训练一个epoch"""
        pass
    
    def validate_epoch(self, model: StaticModel, dataloader: DataLoader, 
                      criterion: nn.Module) -> float:
        """验证一个epoch"""
        pass
    
    def train(self):
        """执行训练流程"""
        print("开始训练静态任务图模型...")
        
        # 准备数据
        train_samples = self.data_loader.get_all_training_data()
        train_dataset = StaticModelDataset(train_samples)
        train_dataloader = DataLoader(train_dataset, 
                                    batch_size=self.config['training']['batch_size'], 
                                    shuffle=True)
        
        # 创建模型
        model = self.create_model().to(self.device)
        optimizer = optim.Adam(model.parameters(), lr=self.config['training']['learning_rate'])
        criterion = nn.CrossEntropyLoss()
        
        # 训练循环
        train_losses = []
        val_losses = []
        
        for epoch in range(self.config['training']['max_epochs']):
            train_loss = self.train_epoch(model, train_dataloader, optimizer, criterion)
            # val_loss = self.validate_epoch(model, val_dataloader, criterion)
            
            train_losses.append(train_loss)
            # val_losses.append(val_loss)
            
            print(f"Epoch {epoch+1}/{self.config['training']['max_epochs']}, "
                  f"Train Loss: {train_loss:.4f}")
        
        # 保存结果
        self.save_results(model, train_losses, val_losses)
        
        # 可视化
        self.visualize_results(train_losses, val_losses)
        
        print("静态任务图模型训练完成!")
    
    def save_results(self, model: StaticModel, train_losses: List, val_losses: List):
        """保存训练结果"""
        pass
    
    def visualize_results(self, train_losses: List, val_losses: List):
        """可视化训练结果"""
        pass

if __name__ == "__main__":
    with open('../config/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    trainer = StaticModelTrainer(config)
    trainer.train()
```

### Train/Train_Dynamic_Model.py

```python
"""动态任务图训练脚本"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from typing import Dict, List, Tuple
from models.mlp_diff import MLPDiff
from utils.data_loader import BreakfastDataLoader
from utils.file_utils import FileManager
from utils.visualization import VisualizationManager
import yaml

class DynamicModelDataset(Dataset):
    """动态模型数据集"""
    
    def __init__(self, samples: List[Dict]):
        self.samples = samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        return {
            'features': torch.FloatTensor(sample['features']),
            'current_action': sample['current_action'],
            'next_action': sample['next_action'],
            'diff': torch.FloatTensor(sample['diff'])
        }

class DynamicModel(nn.Module):
    """动态任务图模型"""
    
    def __init__(self, config: Dict, edge_weights: torch.Tensor, prototypes: torch.Tensor):
        super().__init__()
        self.edge_weights = nn.Parameter(edge_weights, requires_grad=False)  # 冻结
        self.prototypes = nn.Parameter(prototypes, requires_grad=False)  # 冻结
        self.mlp_diff = MLPDiff(config)
    
    def forward(self, features: torch.Tensor, current_actions: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        pass

class DynamicModelTrainer:
    """动态任务图训练器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.data_loader = BreakfastDataLoader(config)
        self.file_manager = FileManager(config['output']['base_path'])
        self.viz_manager = VisualizationManager(config)
        
        # 加载预训练组件
        self.edge_weights = self._load_edge_weights()
        self.prototypes = self._load_prototypes()
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def _load_edge_weights(self) -> torch.Tensor:
        """加载预训练的边权重"""
        pass
    
    def _load_prototypes(self) -> torch.Tensor:
        """加载预训练的原型特征"""
        pass
    
    def create_model(self) -> DynamicModel:
        """创建动态模型"""
        pass
    
    def prepare_training_data(self) -> List[Dict]:
        """准备训练数据（包含diff计算）"""
        pass
    
    def train_epoch(self, model: DynamicModel, dataloader: DataLoader, 
                   optimizer: optim.Optimizer
```
### 6. 视角融合、帧同步与标签映射实现细节

> **本节在不修改既有框架任何行的前提下，给出所有 `pass` 位置所需的核心算法/代码片段，确保疑惑一一落地。**

#### 6.1 视角识别与拼接顺序

```python
# utils/data_loader.py
import re

_CAM_RE = re.compile(r"_cam(\d+)")

def _extract_cam_id(path: str) -> int:
    """从文件名中提取相机编号（默认 cam1 < cam2 < …）。"""
    m = _CAM_RE.search(path)
    return int(m.group(1)) if m else -1  # -1 让无法识别的排在最前面
```
- **排序原则**：按提取到的相机编号升序排序。如此即可令 `MultiViewFusion.fuse_views()` **先拼 cam1 → cam2 → …**，随后在行向量维度上做 `np.mean(axis=0)`。

#### 6.2 帧同步算法

```python
# utils/data_loader.py → MultiViewFusion.sync_frames
@staticmethod
def sync_frames(view_feats: List[np.ndarray]) -> List[np.ndarray]:
    """取各视角帧索引的交集并按升序返回同步后的特征序列。"""
    # 假设第 0 列是显式帧 index，后 64 列为特征
    common_idx = set(view_feats[0][:, 0].astype(int))
    for f in view_feats[1:]:
        common_idx &= set(f[:, 0].astype(int))
    common_idx = sorted(common_idx)
    fused = []
    for f in view_feats:
        idx_to_row = {int(row[0]): row[1:] for row in f}
        fused.append(np.stack([idx_to_row[i] for i in common_idx], axis=0))
    return fused  # 每个元素形状: |common_idx| × 64
```
随后 `fuse_views()` 将 `sync_frames()` 的输出沿 **视角维**(len C) 拼接，再 `np.mean(axis=0)` 保持 **64 维**。

#### 6.3 `label_map.json` 标准格式

```json
{
  "0": "SIL",
  "1": "take_bowl",
  "2": "pour_cereals",
  "3": "pour_milk",
  "4": "stir_cereals",
  "5": "put_down_spoon"
}
```
- **键**：字符串化的整数索引（保持与 numpy `int64` 互转安全）。
- **值**：动作中文或英文名称均可，但 **文件内必须唯一**。
- **自动统计 M**：`BreakfastDataLoader.__init__` 在加载后执行 `self.num_classes = len(self.label_map)` 并向下游暴露。

---

### 7. 模型层面对疑惑的解答与代码钩子

| 疑惑 | 解决方案 | 代码落点 |
|------|----------|----------|
| **MLP_diff 输入维度** | `Diff_k = |V_k - V_prototype|` 与 `V_k` 同为 **64 维**。 | `models/mlp_diff.MLPDiff.__init__` 已固定 `input_dim = config['model']['feature_dim']` (=64)。 |
| **静态模型 bias 维度** | 运行时 `BreakfastDataLoader.num_classes -> M`，在 `StaticModel`、`DynamicModel` 构造函数里直接使用。 | 见下 `StaticModel.__init__` 样例。 |
| **类别不均衡加权** | 交叉熵 `weight` 参数可选：`torch.Tensor(class_freq).reciprocal()`；默认 `None`；在 `config.yaml` 新增 `training.class_balancing: false/true` 开关。 | `Train_Static_Model.py` & `Train_Dynamic_Model.py` → 创建 `criterion` 时读取该开关。 |

```python
# models/base extensions
class StaticModel(nn.Module):
    def __init__(self, edge_weights: torch.Tensor):
        super().__init__()
        num_classes = edge_weights.size(0)
        self.register_buffer('edge_weights', edge_weights)  # 冻结
        self.bias = nn.Parameter(torch.zeros(num_classes))

    def forward(self, current_actions: torch.Tensor):
        # 根据当前动作索引选取对应行
        logits = self.edge_weights[current_actions] + self.bias
        return logits
```

---

### 8. 评估指标实现 (包含 Hungarian 匹配与 SIL 过滤)

```python
# utils/evaluation_metrics.py (关键实现片段)
from scipy.optimize import linear_sum_assignment

SIL_ID = 0  # 假设 0 对应 SIL

class EvaluationMetrics:
    ...
    def _filter_sil(self, segs):
        return [s for s in segs if s[2] != SIL_ID]

    def compute_temporal_iou(self, pred_segs, gt_segs, threshold):
        pred_segs = self._filter_sil(pred_segs)
        gt_segs = self._filter_sil(gt_segs)
        if not pred_segs or not gt_segs:
            return 0.0
        # 构造 IoU 矩阵
        IoU_mat = np.zeros((len(gt_segs), len(pred_segs)))
        for i, g in enumerate(gt_segs):
            gs, ge, _ = g
            for j, p in enumerate(pred_segs):
                ps, pe, _ = p
                inter = max(0, min(ge, pe) - max(gs, ps) + 1)
                union = (ge - gs + 1) + (pe - ps + 1) - inter
                IoU_mat[i, j] = inter / union
        # 负值转成本
        cost = 1 - IoU_mat
        row_ind, col_ind = linear_sum_assignment(cost)
        TP, FP, FN = 0, 0, 0
        for r, c in zip(row_ind, col_ind):
            if IoU_mat[r, c] >= threshold:
                TP += IoU_mat[r, c]  # 累加交集
            else:
                FP += IoU_mat[r, c]
        FN = len(gt_segs) - TP
        union_sum = TP + FP + FN
        return TP / union_sum if union_sum else 0.0
```
- **micro IoU**：跨视频累积 `TP`, `FP`, `FN` 后再统一计算。

#### Bootstrap CI

```python
from functools import partial

    def bootstrap_confidence_interval(self, data, metric_func, n_bootstrap=500, confidence=0.95):
        stats = []
        rng = np.random.default_rng(42)
        N = len(data)
        for _ in range(n_bootstrap):
            sample_idx = rng.choice(N, N, replace=True)
            stats.append(metric_func([data[i] for i in sample_idx]))
        lower = np.percentile(stats, (1 - confidence) / 2 * 100)
        upper = np.percentile(stats, (1 + confidence) / 2 * 100)
        return float(np.mean(stats)), float(lower), float(upper)
```

---

### 9. DynamicModel 训练脚本剩余关键函数

```python
# Train/Train_Dynamic_Model.py (简要示例)
class DynamicModel(nn.Module):
    def forward(self, features, current_actions):
        diff = torch.abs(features - self.prototypes[current_actions])
        delta = self.mlp_diff(diff)
        logits = self.edge_weights[current_actions] + delta
        return logits
```

```python
class DynamicModelTrainer:
    def prepare_training_data(self):
        samples = self.data_loader.get_all_training_data()
        for s in samples:
            cur = s['current_action']
            diff = np.abs(s['features'] - self.prototypes[cur].cpu().numpy())
            s['diff'] = diff
        return samples
```
- **训练/验证 Epoch**：复用 `train_epoch` 写法与静态模型相同，只是输入多了 `features`。

---

### 10. `run_experiment.py` 总控脚本骨架

```python
"""一键跑全流程"""
import subprocess, yaml

STEPS = [
    'txt_to_npy.py',
    'Train/Train_Action_Prototype.py',
    'Train/Train_Edge_Weight.py',
    'Train/Train_Static_Model.py',
    'Train/Train_Dynamic_Model.py',
    'Test/Test_Static.py',
    'Test/Test_Dynamic.py',
    'Test/Test_Static_vs_Dynamic.py'
]

if __name__ == '__main__':
    for step in STEPS:
        print(f'>>> Running {step}')
        subprocess.run(['python', step], check=True)
```

---

### 11. 更新后的 `config.yaml` 片段

```yaml
training:
  ...
  class_balancing: false   # 若为 true 将自动按 1/freq 加权
```

---

> **至此，所有用户提出的疑惑均已在代码层面给出可复用实现示例，原有文件段落未作任何改动。后续填充业务逻辑时可直接复制粘贴上述片段替换各 `pass`。

